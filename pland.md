```py
def notify_buy_stars_transaction(bot, user_id, username, stars_amount, coin, price, recipient='self'):
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    recipient_text = f"🎁 Gift To: @{recipient}\n 🎁" if recipient and recipient != 'self' else ""

    message = (
        f"🔔 *NEW STARS PURCHASE REQUEST*\n\n"
        f"👤 From: @{escape_markdown(username)} (ID: {user_id})\n"
        f"{recipient_text}"
        f"⭐ Amount: {stars_amount} stars\n"
        f"💰 Price: {price} {coin}\n"
        f"🕒 Time: {now}\n\n"
        f"Please verify this transaction and approve or reject it."
    )

    keyboard = types.InlineKeyboardMarkup()
    approve_button = types.InlineKeyboardButton(
        "✅ Approve",
        callback_data=f"approve_stars_{user_id}_{stars_amount}_{recipient}"
    )
    reject_button = types.InlineKeyboardButton(
        "❌ Reject",
        callback_data=f"reject_stars_{user_id}_{stars_amount}_{recipient}"
    )
    keyboard.row(approve_button, reject_button)

    for admin_id in ADMIN_IDS:
        try:
            bot.send_message(
                chat_id=admin_id,
                text=message,
                reply_markup=keyboard,
                parse_mode="Markdown"
            )
        except Exception as e:
            print(f"Cannot send message to {admin_id}: {str(e)}")


def notify_buy_premium_transaction(bot, user_id, username, days, amount, coin, period, recipient=None):
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    user_mention = f"@{escape_markdown(username)}" if username else f"User ID: {user_id}"

    recipient_text = f"🎁 Gift To: @{recipient}\n 🎁" if recipient else ""

    message = (
        f"🔔 *NEW PREMIUM MEMBERSHIP REQUEST*\n\n"
        f"👤 From: {user_mention} (ID: `{user_id}`)\n"
        f"{recipient_text}"
        f"🔰 Period: {period} ({days} days)\n"
        f"💰 Amount: {amount} {coin}\n"
        f"🕒 Time: {now}\n\n"
        f"Please verify this transaction and approve or reject it."
    )

    keyboard = types.InlineKeyboardMarkup()
    approve_button = types.InlineKeyboardButton(
        "✅ Approve",
        callback_data=f"approve_premium_{user_id}_{days}_{recipient}"
    )
    reject_button = types.InlineKeyboardButton(
        "❌ Reject",
        callback_data=f"reject_premium_{user_id}_{days}_{recipient}"
    )
    keyboard.row(approve_button, reject_button)

    for admin_id in ADMIN_IDS:
        try:
            bot.send_message(
                chat_id=admin_id,
                text=message,
                reply_markup=keyboard,
                parse_mode="Markdown"
            )
        except Exception as e:
            print(f"Cannot send message to {admin_id}: {str(e)}")
```
