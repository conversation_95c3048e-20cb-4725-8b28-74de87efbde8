<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\TelegramCommands\AdminMiddleware;
use <PERSON><PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramBot\Services\MessageContextService;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramTonPayment\Models\VerificationTransaction;

class ApproveWithdrawalCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $withdrawalId = $params[0] ?? null;
            if (!$withdrawalId) {
                DiscordLogHelper::error('ApproveWithDrawl: Missing withdrawal ID in parameters');
                return ['success' => false, 'handled' => false];
            }

            try {
                VerificationTransaction::where('id', $withdrawalId)
                    ->update(['status' => 'approved']);

                $botClient = new TelegramBotClient;

                DiscordLogHelper::log('Approving withdrawal ID '.$withdrawalId.' by admin '.$user->tele_id);

                $message = "✅ Withdrawal ID {$withdrawalId} has been approved.";
                $botClient->sendMessage(
                    $chatId,
                    $message,
                    [
                        'parse_mode' => 'Markdown',
                    ]
                );

                // Delete the original notification message
                $this->deleteOriginalMessage($botClient, $chatId);

            } catch (\Exception $e) {
                DiscordLogHelper::error('ApproveWithDrawl failed: '.$e->getMessage());
                DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

                return ['success' => false, 'handled' => false];
            }

            return ['success' => true, 'handled' => true];
        });
    }

    /**
     * Delete the original withdrawal notification message
     *
     * @param TelegramBotClient $botClient
     * @param string|int $chatId
     * @return void
     */
    private function deleteOriginalMessage(TelegramBotClient $botClient, string|int $chatId): void
    {
        try {
            $messageId = MessageContextService::getMessageId();

            if ($messageId) {
                $deleteResult = $botClient->deleteMessage($chatId, $messageId);

                if ($deleteResult['success']) {
                    DiscordLogHelper::log('Successfully deleted withdrawal notification message');
                } else {
                    DiscordLogHelper::error('Failed to delete withdrawal notification message', [
                        'chat_id' => $chatId,
                        'message_id' => $messageId,
                        'error' => $deleteResult['error'] ?? 'Unknown error'
                    ]);
                }
            } else {
                DiscordLogHelper::error('No message ID available for deletion');
            }
        } catch (\Exception $e) {
            DiscordLogHelper::error('Exception while deleting withdrawal notification message', [
                'chat_id' => $chatId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
