<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Modules\TelegramBot\TelegramCommands\AdminMiddleware;
use <PERSON><PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Modules\TelegramBot\Services\MessageContextService;
use Modules\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramTonPayment\Models\VerificationTransaction;

class RejectWithdrawalCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $withdrawalId = $params[0] ?? null;
            if (! $withdrawalId) {
                DiscordLogHelper::error('RejectWithdrawal: Missing withdrawal ID in parameters');

                return ['success' => false, 'handled' => false];
            }

            try {
                VerificationTransaction::where('id', $withdrawalId)
                    ->update(['status' => 'rejected']);

                $botClient = new TelegramBotClient;

                DiscordLogHelper::log('Rejecting withdrawal ID '.$withdrawalId.' by admin '.$user->tele_id);

                $message = "❌ Withdrawal ID {$withdrawalId} has been rejected.";
                $botClient->sendMessage(
                    $chatId,
                    $message,
                    [
                        'parse_mode' => 'Markdown',
                    ]
                );

                // Delete the original notification message
                $this->deleteOriginalMessage($botClient, $chatId);

            } catch (\Exception $e) {
                DiscordLogHelper::error('RejectWithdrawal failed: '.$e->getMessage());
                DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

                return ['success' => false, 'handled' => false];
            }

            return ['success' => true, 'handled' => true];
        });
    }

    /**
     * Delete the original withdrawal notification message
     *
     * @param TelegramBotClient $botClient
     * @param string|int $chatId
     * @return void
     */
    private function deleteOriginalMessage(TelegramBotClient $botClient, string|int $chatId): void
    {
        try {
            $messageId = MessageContextService::getMessageId();
            
            if ($messageId) {
                $deleteResult = $botClient->deleteMessage($chatId, $messageId);
                
                if ($deleteResult['success']) {
                    DiscordLogHelper::log('Successfully deleted withdrawal notification message', [
                        'chat_id' => $chatId,
                        'message_id' => $messageId
                    ]);
                } else {
                    DiscordLogHelper::error('Failed to delete withdrawal notification message', [
                        'chat_id' => $chatId,
                        'message_id' => $messageId,
                        'error' => $deleteResult['error'] ?? 'Unknown error'
                    ]);
                }
            } else {
                DiscordLogHelper::warning('No message ID available for deletion');
            }
        } catch (\Exception $e) {
            DiscordLogHelper::error('Exception while deleting withdrawal notification message', [
                'chat_id' => $chatId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
