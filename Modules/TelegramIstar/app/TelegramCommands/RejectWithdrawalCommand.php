<?php

namespace Modules\TelegramIstar\TelegramCommands;

use App\Helpers\DiscordLogHelper;
use <PERSON><PERSON>les\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\TelegramCommands\AdminMiddleware;
use <PERSON><PERSON>les\TelegramBot\TelegramCommands\CommandInterface;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Modules\TelegramTonPayment\Models\VerificationTransaction;

class RejectWithdrawalCommand implements CommandInterface
{
    public function handle(string|int $chatId, TelegramUser $user, ?array $params = []): array
    {
        return AdminMiddleware::check($user, $chatId, function () use ($chatId, $user, $params) {
            $withdrawalId = $params[0] ?? null;
            if (! $withdrawalId) {
                DiscordLogHelper::error('RejectWithdrawal: Missing withdrawal ID in parameters');

                return ['success' => false, 'handled' => false];
            }

            try {
                VerificationTransaction::where('id', $withdrawalId)
                    ->update(['status' => 'rejected']);

                $botClient = new TelegramBotClient;

                DiscordLogHelper::log('Rejecting withdrawal ID '.$withdrawalId.' by admin '.$user->tele_id);

                $message = "❌ Withdrawal ID {$withdrawalId} has been rejected.";
                $botClient->sendMessage(
                    $chatId,
                    $message,
                    [
                        'parse_mode' => 'Markdown',
                    ]
                );

            } catch (\Exception $e) {
                DiscordLogHelper::error('RejectWithdrawal failed: '.$e->getMessage());
                DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

                return ['success' => false, 'handled' => false];
            }

            return ['success' => true, 'handled' => true];
        });
    }
}
