<?php

namespace Modules\TelegramIstar\Views;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Components\BackToLastCommand;
use Modules\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;
use Mo<PERSON>les\TelegramIstar\Components\BackToMenu;
use Modules\TelegramIstar\Services\ThemeService;
use Modules\TelegramIstar\Components\GiftForSomeone;
use Modules\TelegramIstar\Components\BuyForMyself;

class BuyStarsMenuView implements ViewInterface
{
    protected $themeService;

    public function __construct()
    {
        $this->themeService = new ThemeService;
    }

    /**
     * Handle the buy stars menu display
     */
    public function show(string|int $chatId, TelegramUser $user, ?array $params = []): void
    {
        try {
            $botClient = new TelegramBotClient;

            DiscordLogHelper::log('Showing buy stars menu to user '.$user->tele_id);

            // Create the buy stars menu description
            $description = $this->getBuyStarsDescription();

            // Create inline keyboard for buy stars menu
            $keyboard = $this->createBuyStarsKeyboard($user);

            DiscordLogHelper::log('Sending buy stars menu to user '.$user->tele_id);

            $botClient->sendMessage(
                $chatId,
                $description,
                [
                    'reply_markup' => json_encode($keyboard),
                    'parse_mode' => 'Markdown',
                ]
            );
        } catch (\Exception $e) {
            DiscordLogHelper::error('BuyStarsMenuView failed: '.$e->getMessage());
            DiscordLogHelper::error('Stack trace: '.$e->getTraceAsString());

            $view = new FallbackView;
            $view->show($chatId, $user);
        }
    }

    /**
     * Get the buy stars menu description
     */
    private function getBuyStarsDescription(): string
    {
        return "💫 Buy Stars ⭐\n\n"
            ."You can purchase stars using TON cryptocurrency.\n"
            ."After your payment is confirmed, stars will be added to the account balance.\n\n"
            .'Please select who you want to buy stars for:';
    }

    /**
     * Create the inline keyboard for buy stars menu
     */
    private function createBuyStarsKeyboard(TelegramUser $user): array
    {
        return [
            'inline_keyboard' => [
                [
                    BuyForMyself::make(['callback_data' => 'show_star_shop']),
                ],
                [
                    GiftForSomeone::make(['callback_data' => 'buy_stars_for_others']),
                ],
                [
                    BackToLastCommand::make(['user' => $user]),
                ],
            ],
        ];
    }
}
