<?php

namespace Modules\TelegramBot\Services;

use App\Helpers\DiscordLogHelper;
use Mo<PERSON>les\TelegramClient\Services\TelegramBotClient;

/**
 * Service to store and retrieve message context for callback commands
 * This allows commands to access the original message data for operations like deletion
 */
class MessageContextService
{
    private static ?array $currentMessageContext = null;

    /**
     * Set the current message context
     */
    public static function setMessageContext(?array $messageContext): void
    {
        self::$currentMessageContext = $messageContext;
    }

    /**
     * Get the current message context
     */
    public static function getMessageContext(): ?array
    {
        return self::$currentMessageContext;
    }

    /**
     * Get the message ID from the current context
     */
    public static function getMessageId(): ?int
    {
        return self::$currentMessageContext['message_id'] ?? null;
    }

    /**
     * Get the chat ID from the current context
     */
    public static function getChatId(): int|string|null
    {
        return self::$currentMessageContext['chat']['id'] ?? null;
    }

    /**
     * Clear the message context
     */
    public static function clearMessageContext(): void
    {
        self::$currentMessageContext = null;
    }

    /**
     * Delete the original message from the current context
     */
    public static function deleteOriginalMessage(TelegramBotClient $botClient, string|int $chatId): void
    {
        try {
            $messageId = self::getMessageId();

            if ($messageId) {
                $deleteResult = $botClient->deleteMessage($chatId, $messageId);

                if ($deleteResult['success']) {
                    DiscordLogHelper::log('Successfully deleted withdrawal notification message');
                } else {
                    DiscordLogHelper::error('Failed to delete withdrawal notification message', [
                        'chat_id' => $chatId,
                        'message_id' => $messageId,
                        'error' => $deleteResult['error'] ?? 'Unknown error',
                    ]);
                }
            } else {
                DiscordLogHelper::error('No message ID available for deletion');
            }
        } catch (\Exception $e) {
            DiscordLogHelper::error('Exception while deleting withdrawal notification message', [
                'chat_id' => $chatId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
