<?php

namespace Modules\TelegramBot\Services;

/**
 * Service to store and retrieve message context for callback commands
 * This allows commands to access the original message data for operations like deletion
 */
class MessageContextService
{
    private static ?array $currentMessageContext = null;

    /**
     * Set the current message context
     *
     * @param array|null $messageContext
     * @return void
     */
    public static function setMessageContext(?array $messageContext): void
    {
        self::$currentMessageContext = $messageContext;
    }

    /**
     * Get the current message context
     *
     * @return array|null
     */
    public static function getMessageContext(): ?array
    {
        return self::$currentMessageContext;
    }

    /**
     * Get the message ID from the current context
     *
     * @return int|null
     */
    public static function getMessageId(): ?int
    {
        return self::$currentMessageContext['message_id'] ?? null;
    }

    /**
     * Get the chat ID from the current context
     *
     * @return int|string|null
     */
    public static function getChatId(): int|string|null
    {
        return self::$currentMessageContext['chat']['id'] ?? null;
    }

    /**
     * Clear the message context
     *
     * @return void
     */
    public static function clearMessageContext(): void
    {
        self::$currentMessageContext = null;
    }
}
