<?php

namespace Modules\TelegramBot\Services;

use App\Helpers\DiscordLogHelper;
use Modules\TelegramBot\Models\TelegramUser;
use Mo<PERSON>les\TelegramBot\Services\TelegramBotService;

class QueryCallbackDispatcher
{
    public function __construct() {}

    /**
     * Dispatch a callback query to its corresponding handler
     *
     * @param  string  $callbackData  The callback data from the inline button
     * @param  array  $callbackQuery  The Telegram callback query data
     * @param  TelegramUser  $user  The user who triggered the callback
     * @return array Response array with success status and handled flag
     */
    public function dispatch(array $callbackQuery, TelegramUser $user): array
    {
        $data = $this->parseData($callbackQuery['data'] ?? '');
        $command = $data['command'] ?? null;
        if (! $command) {
            DiscordLogHelper::error('No command found in callback data', [
                'callback_query' => $callbackQuery,
                'user_id' => $user->tele_id,
            ]);
            return ['success' => false, 'handled' => false];
        }

        $user->last_command = $command;
        $user->save();

        $message = $callbackQuery['message'] ?? [];

        $service = new TelegramBotService();

        try {
            $handler = $service->commandLoader($command);
            if (! $handler) {
                DiscordLogHelper::error('No handler found for command', [
                    'command' => $command,
                    'user_id' => $user->tele_id,
                    'callback_query' => $callbackQuery,
                ]);

                return ['success' => false, 'handled' => false];
            }

            return $handler->handle($message['chat']['id'], $user, $data['params'] ?? []);

        } catch (\Exception $e) {
            DiscordLogHelper::error('Error dispatching query callback', [
                'callback_query' => $callbackQuery,
                'user_id' => $user->tele_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ['success' => false, 'handled' => false];
        }

        return ['success' => true, 'handled' => true];
    }

    private function parseData(string $data): array
    {
        $parts = explode('-', $data);
        $command = array_shift($parts);
        return ['command' => $command, 'params' => empty($parts) ? [] : explode('_', $parts[0])];
    }
}
